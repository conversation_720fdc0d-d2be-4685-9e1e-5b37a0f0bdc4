{"name": "gita-ai-frontend", "private": true, "version": "2.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test-production": "node scripts/test-production-endpoints.js"}, "dependencies": {"@clerk/clerk-react": "^5.35.3", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@react-three/drei": "^9.114.0", "@react-three/fiber": "^8.17.10", "@react-three/postprocessing": "^2.16.2", "@supabase/supabase-js": "^2.52.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "lucide-react": "^0.294.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-hook-form": "^7.47.0", "react-router-dom": "^6.18.0", "sonner": "^1.7.4", "tailwind-merge": "^2.0.0", "three": "^0.158.0", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/three": "^0.158.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "terser": "^5.43.1", "typescript": "^5.2.2", "vite": "^7.0.5", "vite-plugin-node-polyfills": "^0.24.0"}}