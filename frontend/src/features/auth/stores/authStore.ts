import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useUser, useAuth } from '@clerk/clerk-react';
import { User } from '../../../shared/types';
import { storage, STORAGE_KEYS } from '../../../shared/utils';
import { cacheService, CACHE_TTL, CACHE_PATTERNS } from '../../../services/cacheService';

interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  setUser: (user: User) => void;
  setTokens: (token: string, refreshToken: string) => void;
  clearAuth: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshAuthToken: () => Promise<void>;
}

type AuthStore = AuthState & AuthActions;

const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      setUser: (user) => set({ user, isAuthenticated: true }),

      setTokens: (token, refreshToken) => {
        storage.set(STORAGE_KEYS.AUTH_TOKEN, token);
        storage.set(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
        set({ token, refreshToken, isAuthenticated: true });
      },

      clearAuth: () => {
        storage.remove(STORAGE_KEYS.AUTH_TOKEN);
        storage.remove(STORAGE_KEYS.REFRESH_TOKEN);
        storage.remove(STORAGE_KEYS.USER_DATA);
        set(initialState);
      },

      setLoading: (isLoading) => set({ isLoading }),

      setError: (error) => set({ error }),

      login: async (email, password) => {
        set({ isLoading: true, error: null });
        try {
          // Mock login - replace with actual API call
          const mockUser: User = {
            id: '1',
            name: 'Demo User',
            email,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            subscription: {
              plan: 'free',
              status: 'active',
            },
          };
          
          const mockToken = 'mock-jwt-token';
          const mockRefreshToken = 'mock-refresh-token';
          
          get().setUser(mockUser);
          get().setTokens(mockToken, mockRefreshToken);
          set({ isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Login failed' 
          });
          throw error;
        }
      },

      register: async (name, email, password) => {
        set({ isLoading: true, error: null });
        try {
          // Mock registration - replace with actual API call
          const mockUser: User = {
            id: '1',
            name,
            email,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            subscription: {
              plan: 'free',
              status: 'active',
            },
          };
          
          const mockToken = 'mock-jwt-token';
          const mockRefreshToken = 'mock-refresh-token';
          
          get().setUser(mockUser);
          get().setTokens(mockToken, mockRefreshToken);
          set({ isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Registration failed' 
          });
          throw error;
        }
      },

      logout: () => {
        get().clearAuth();
      },

      refreshAuthToken: async () => {
        const { refreshToken } = get();
        if (!refreshToken) {
          get().clearAuth();
          return;
        }

        try {
          // Mock refresh - replace with actual API call
          const mockToken = 'new-mock-jwt-token';
          storage.set(STORAGE_KEYS.AUTH_TOKEN, mockToken);
          set({ token: mockToken });
        } catch (error) {
          get().clearAuth();
          throw error;
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
