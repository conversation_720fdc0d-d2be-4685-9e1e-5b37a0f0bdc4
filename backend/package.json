{"name": "gita-gpt", "version": "1.0.0", "description": "Backend for the GITAGPT app - AI-powered Bhagavad Gita advisor with speech synthesis", "main": "src/app.js", "type": "module", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js --ext js", "build": "echo \"No build step required\"", "health": "curl -f http://localhost:3000/ping || exit 1", "production-check": "node scripts/production-check.js", "prestart": "node scripts/production-check.js"}, "keywords": ["ai", "bhagavad-gita", "chatbot", "speech-synthesis", "spiritual-advisor"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@clerk/clerk-sdk-node": "^4.13.23", "@elevenlabs/elevenlabs-js": "^2.6.0", "@google/generative-ai": "^0.24.1", "@supabase/supabase-js": "^2.52.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.0.1", "helmet": "^7.2.0", "ioredis": "^5.6.1", "morgan": "^1.10.1", "node-cron": "^4.2.1", "node-fetch": "^3.3.2", "openai": "^4.28.4", "svix": "^1.69.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"nodemon": "^3.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}