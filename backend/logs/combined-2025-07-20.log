{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:23:10"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:23:11"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:23:11"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:23:11"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:23:12"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:23:12"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:23:18"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:23:19"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:23:19"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:23:20"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:23:20"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:23:20"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:24:30"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:24:31"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:24:31"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:24:32"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:24:32"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:24:32"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:28:26"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:28:26"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:28:27"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:28:27"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:28:27"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:28:27"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:36:38"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:36:39"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:36:39"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:36:40"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:36:40"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:36:40"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:54:57"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:54:58"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:54:58"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:54:58"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:54:58"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:54:58"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:57:51"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:57:51"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:57:51"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:57:51"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:57:52"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:57:52"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:58:22"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:58:23"}
{"level":"warn","message":"Cache service unavailable, running without Redis:","service":"gita-chatbot","timestamp":"2025-07-20 19:58:27"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:58:29"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:58:29"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:58:31"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:58:56"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:58:57"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:58:57"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:58:58"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:58:58"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:58:58"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:59:28"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:59:28"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:59:28"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:59:29"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 19:59:29"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 19:59:29"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:02:20"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:02:21"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:02:22"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:02:22"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:02:22"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:02:22"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:02:44"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:02:44"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:02:44"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:02:44"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:02:44"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:02:44"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:03:10"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:03:10"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:03:11"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:03:11"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:03:11"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:03:11"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:03:42"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:03:42"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:03:42"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:03:43"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:03:43"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:03:43"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:03:59"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:03:59"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:03:59"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:04:00"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:04:00"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:04:00"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:04:33"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:04:34"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:04:34"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:04:34"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:04:34"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:04:35"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:05:35"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:05:36"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:05:36"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:05:37"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:05:37"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:05:37"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:20"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:09:22"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:22"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:22"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:09:22"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:22"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:33"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:09:34"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:34"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:34"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:09:34"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:35"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:48"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:09:48"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:49"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:49"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:09:49"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:09:49"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:28:03"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:28:04"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:28:04"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:28:05"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:28:06"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:28:06"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:28:37"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:28:37"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:28:38"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:28:38"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:28:38"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:28:38"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:29:21"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:29:24"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:29:24"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:29:24"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:29:24"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:29:24"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:29:57"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:29:57"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:29:57"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:29:58"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:29:58"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:29:58"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:30:26"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:30:26"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:30:26"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:30:27"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:30:27"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:30:27"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:30:42"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:30:42"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:30:43"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:30:43"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:30:43"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:30:43"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:38:38"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:38:39"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:38:39"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:38:40"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:38:40"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:38:40"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:38:55"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:38:56"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:38:56"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:38:56"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:38:56"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:38:56"}
{"clerkId":"user_308zHO9FOk4gnFouRBgUeDYawhk","email":"<EMAIL>","level":"info","message":"🔄 DB Operation: Creating user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-20 20:41:33","username":"keshav_0927"}
{"clerkId":"user_308zHO9FOk4gnFouRBgUeDYawhk","email":"<EMAIL>","level":"info","message":"✅ DB Operation: User created successfully","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-20 20:41:33","userId":"ba804a44-0e53-452a-85fc-5258fa3a076a"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:46:06"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:46:06"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:46:07"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:46:07"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:46:07"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:46:07"}
{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","email":"<EMAIL>","level":"info","message":"🔄 DB Operation: Creating user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-20 20:46:21","username":"keshav0927"}
{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","email":"<EMAIL>","level":"info","message":"✅ DB Operation: User created successfully","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-20 20:46:21","userId":"7897f702-4f6d-44ba-abc0-b9c61cd63a8c"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:49:57"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:49:58"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:49:58"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:49:59"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:49:59"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:49:59"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:50:15"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:50:15"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:50:16"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:50:16"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:50:16"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:50:16"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:50:41"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:50:42"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:50:43"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:50:43"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:50:43"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:50:43"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:51:19"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:51:20"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:51:20"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:51:21"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 20:51:21"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 20:51:21"}
{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","email":"<EMAIL>","level":"info","message":"🔄 DB Operation: Creating user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-20 20:51:34","username":"keshav0927"}
{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","error":"duplicate key value violates unique constraint \"users_clerk_id_key\"","level":"error","message":"❌ DB Operation: Failed to create user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-20 20:51:34"}
{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","email":"<EMAIL>","level":"info","message":"🔄 DB Operation: Creating user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-20 21:09:48","username":"keshav0927"}
{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","error":"duplicate key value violates unique constraint \"users_clerk_id_key\"","level":"error","message":"❌ DB Operation: Failed to create user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-20 21:09:49"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 21:54:51"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 21:54:51"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 21:54:51"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 21:54:51"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 21:54:52"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 21:54:52"}
{"email":"<EMAIL>","endpoint":"/","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 22:16:36","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:22:37"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:22:38"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:22:38"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:22:39"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:22:39"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:22:39"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:22:56"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:22:57"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:22:57"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:22:57"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:22:57"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:22:58"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:23:20"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:23:20"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:23:20"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:23:21"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:23:22"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:23:22"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:23:46"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:23:47"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:23:48"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:23:48"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:23:49"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:23:49"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:24:01"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:24:02"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:24:02"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:24:02"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:24:03"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:24:03"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:28:18"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:28:19"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:28:19"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:28:19"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:28:19"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:28:20"}
{"level":"info","message":"🔄 DB Operation: Creating conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:28:28","title":"New Chat","userId":"test-user-id"}
{"error":"invalid input syntax for type uuid: \"test-user-id\"","level":"error","message":"❌ DB Operation: Failed to create conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:28:28","title":"New Chat","userId":"test-user-id"}
{"email":"<EMAIL>","endpoint":"/","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 22:28:44","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:30:37"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:30:38"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:30:38"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:30:49"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:31:10"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:31:12"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:31:12"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:31:13"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:31:13"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:31:13"}
{"email":"<EMAIL>","endpoint":"/","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 22:32:14","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:36:32"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:36:33"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:36:33"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:36:34"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:36:34"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:36:34"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:36:46"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:36:47"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:36:48"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:36:48"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:36:48"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:36:48"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:37:33"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:37:34"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:37:34"}
{"email":"<EMAIL>","endpoint":"/","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 22:40:11","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
{"level":"info","message":"🔄 DB Operation: Creating conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:41:49","title":"New Chat","userId":"test-user-id"}
{"error":"invalid input syntax for type uuid: \"test-user-id\"","level":"error","message":"❌ DB Operation: Failed to create conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:41:49","title":"New Chat","userId":"test-user-id"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:42:15"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:42:16"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:42:16"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:42:17"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:42:17"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:42:17"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:44:17"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:44:18"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:44:18"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:44:18"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:44:18"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:44:18"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:45:01"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:45:02"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:45:02"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:45:02"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:45:02"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:45:03"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:45:54"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:45:55"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:45:55"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:45:56"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:45:56"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:45:56"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:46:48"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:46:48"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:46:49"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:46:49"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:46:49"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:46:49"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:47:46"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:47:47"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:47:48"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:47:48"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:47:48"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:47:48"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:48:00"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:48:01"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:48:01"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:48:01"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:48:02"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:48:02"}
{"level":"info","message":"🔄 DB Operation: Creating conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:49:50","title":"New Chat","userId":"test-user-id"}
{"error":"invalid input syntax for type uuid: \"test-user-id\"","level":"error","message":"❌ DB Operation: Failed to create conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:49:51","title":"New Chat","userId":"test-user-id"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:50:47"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:50:48"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:50:48"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:51:31"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:51:32"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:51:32"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:51:32"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:51:32"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:51:33"}
{"level":"info","message":"🔄 DB Operation: Creating conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:51:54","title":"New Chat","userId":"test-user-id"}
{"error":"invalid input syntax for type uuid: \"test-user-id\"","level":"error","message":"❌ DB Operation: Failed to create conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:51:55","title":"New Chat","userId":"test-user-id"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:52:24"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:52:24"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:52:24"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:52:25"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:52:25"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:52:25"}
{"level":"info","message":"🔄 DB Operation: Creating conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:52:37","title":"New Chat","userId":"550e8400-e29b-41d4-a716-446655440000"}
{"error":"insert or update on table \"conversations\" violates foreign key constraint \"conversations_user_id_fkey\"","level":"error","message":"❌ DB Operation: Failed to create conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:52:37","title":"New Chat","userId":"550e8400-e29b-41d4-a716-446655440000"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:53:12"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:53:13"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:53:13"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:53:13"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:53:14"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:53:14"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:26"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:55:27"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:27"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:28"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:55:28"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:28"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:41"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:55:41"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:42"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:42"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:55:42"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:42"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:57"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:55:57"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:58"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:58"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:55:58"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:55:58"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:56:21"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:56:22"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:56:22"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:56:23"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:56:23"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:56:23"}
{"email":"<EMAIL>","endpoint":"/","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 22:57:27","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:59:43"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:59:44"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:59:44"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:59:45"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 22:59:45"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 22:59:45"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:00:20"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:00:21"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:00:22"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:00:22"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:00:22"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:00:23"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:02:50"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:02:51"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:02:52"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:02:52"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:02:52"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:02:53"}
{"email":"<EMAIL>","endpoint":"/fast","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 23:04:36","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
{"email":"<EMAIL>","endpoint":"/fast","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 23:06:09","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:11:24"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:11:25"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:11:26"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:11:26"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:11:26"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:11:26"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:13:45"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:13:46"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:13:46"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:13:47"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:13:47"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:13:47"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:14:09"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:14:11"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:14:11"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:14:12"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:14:12"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:14:12"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:15:05"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:15:06"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:15:06"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:15:07"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:15:07"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:15:07"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:15:19"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:15:21"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:15:21"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:15:21"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:15:21"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:15:22"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:16:23"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:16:24"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:16:24"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:16:25"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:16:25"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:16:25"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:16:48"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:16:48"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:16:48"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:16:49"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:16:49"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:16:49"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:04"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:17:04"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:05"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:05"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:17:05"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:05"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:31"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:17:32"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:32"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:32"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:17:32"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:33"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:57"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:17:58"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:58"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:59"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:17:59"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:17:59"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:18:19"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:18:19"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:18:19"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:18:20"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:18:20"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:18:20"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:18:30"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:18:30"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:18:31"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:18:31"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:18:31"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:18:31"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:19:02"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:19:03"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:19:03"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:19:03"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:19:04"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:19:04"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:19:12"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:19:13"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:19:13"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:19:13"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:19:13"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:19:13"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:19:38"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:19:39"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:19:39"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:20:33"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:20:34"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:20:34"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:20:34"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:20:34"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:20:35"}
                                                                                                                                                                                                            {"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:25:53"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:25:54"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:25:54"}
{"level":"error","message":"Failed to initialize database service: Supabase configuration missing","service":"gita-chatbot","stack":"Error: Supabase configuration missing\n    at DatabaseService.initialize (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:16:15)\n    at initializeServices (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/app.js:42:42)\n    at startServer (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/app.js:111:9)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/app.js:206:3","timestamp":"2025-07-20 23:26:17"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:26:17"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:26:17"}
{"level":"error","message":"Failed to initialize database service: Supabase configuration missing","service":"gita-chatbot","stack":"Error: Supabase configuration missing\n    at DatabaseService.initialize (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:16:15)\n    at Server.<anonymous> (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/app.js:129:46)\n    at Object.onceWrapper (node:events:638:28)\n    at Server.emit (node:events:536:35)\n    at emitListeningNT (node:net:1955:10)\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","timestamp":"2025-07-20 23:26:17"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:26:17"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:26:17"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:27:00"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:27:01"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:27:01"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:27:02"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:27:02"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:27:02"}
{"email":"<EMAIL>","endpoint":"/","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 23:27:56","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:30:59"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:31:00"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:31:00"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:31:40"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:31:41"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:31:41"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:39:24"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:39:25"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:39:25"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:39:25"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:39:25"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:39:25"}
{"email":"<EMAIL>","endpoint":"/","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 23:40:44","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:43:53"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:43:53"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:43:53"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:43:53"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:43:54"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:43:54"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:13"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:44:14"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:14"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:15"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:44:15"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:15"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:24"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:44:25"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:25"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:25"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:44:25"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:25"}
{"email":"<EMAIL>","endpoint":"/","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 23:44:48","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:55"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:44:56"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:56"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:56"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:44:56"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:44:56"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:06"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:45:07"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:07"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:07"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:45:07"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:07"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:44"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:45:44"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:44"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:45"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:45:45"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:45"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:54"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:45:55"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:55"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:55"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:45:55"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:45:55"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:46:18"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:46:18"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:46:19"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:46:19"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:46:19"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:46:19"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:46:26"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:46:26"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:46:26"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:46:35"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:46:36"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:46:36"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:11"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:47:13"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:13"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:13"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:47:13"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:14"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:44"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:44"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:47:44"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:47:44"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:44"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:45"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:45"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:47:45"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:45"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:47:45"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:45"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:47:45"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:48:49"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:48:49"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:48:49"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:48:49"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:48:50"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:48:50"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:48:50"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:48:50"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:48:50"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:48:50"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:48:50"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:48:50"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:13"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:13"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:50:14"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:50:14"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:14"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:14"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:14"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:50:14"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:14"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:50:14"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:15"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:15"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:19"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:26"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:50:27"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:27"}
{"level":"info","message":"Database service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:27"}
{"level":"info","message":"Redis connected","service":"gita-chatbot","timestamp":"2025-07-20 23:50:27"}
{"level":"info","message":"Cache service initialized successfully","service":"gita-chatbot","timestamp":"2025-07-20 23:50:28"}
{"email":"<EMAIL>","endpoint":"/","level":"info","message":"User authenticated","service":"gita-chatbot","timestamp":"2025-07-20 23:52:35","userId":"user_308zHO9FOk4gnFouRBgUeDYawhk"}
