{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:24:54"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:24:54","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:24:56"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:24:56"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:24:56","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:24:57"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:24:59"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:24:59","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:25:00"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:25:33"}
{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","error":"duplicate key value violates unique constraint \"users_clerk_id_key\"","level":"error","message":"❌ DB Operation: Failed to create user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-24 21:25:35"}
{"endpoint":"/stats","error":{"message":"duplicate key value violates unique constraint \"users_clerk_id_key\""},"level":"error","message":"duplicate key value violates unique constraint \"users_clerk_id_key\"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:25:35","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at async DatabaseService.getUserStats (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:873:57)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/UserController.js:218:21","hint":"","level":"error","message":"Error getting user stats: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:30:39"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:43:58"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:43:58","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:43:59"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:43:59"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:43:59","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:00"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:02"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:44:02","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:04"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:07"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:44:07","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:08"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:21"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:44:21","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:21"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:22"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:44:22","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:22"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:24"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:44:24","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:24"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:97:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:28"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:102:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-24 21:44:28","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async DatabaseService.createLog (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:614:26)\n    at async DatabaseTransport.processQueue (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/logger.js:87:11)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-24 21:44:28"}
