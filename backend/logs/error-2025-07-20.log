{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","error":"duplicate key value violates unique constraint \"users_clerk_id_key\"","level":"error","message":"❌ DB Operation: Failed to create user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-20 20:51:34"}
{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","error":"duplicate key value violates unique constraint \"users_clerk_id_key\"","level":"error","message":"❌ DB Operation: Failed to create user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-20 21:09:49"}
{"error":"invalid input syntax for type uuid: \"test-user-id\"","level":"error","message":"❌ DB Operation: Failed to create conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:28:28","title":"New Chat","userId":"test-user-id"}
{"error":"invalid input syntax for type uuid: \"test-user-id\"","level":"error","message":"❌ DB Operation: Failed to create conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:41:49","title":"New Chat","userId":"test-user-id"}
{"error":"invalid input syntax for type uuid: \"test-user-id\"","level":"error","message":"❌ DB Operation: Failed to create conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:49:51","title":"New Chat","userId":"test-user-id"}
{"error":"invalid input syntax for type uuid: \"test-user-id\"","level":"error","message":"❌ DB Operation: Failed to create conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:51:55","title":"New Chat","userId":"test-user-id"}
{"error":"insert or update on table \"conversations\" violates foreign key constraint \"conversations_user_id_fkey\"","level":"error","message":"❌ DB Operation: Failed to create conversation","operation":"INSERT","service":"gita-chatbot","table":"conversations","timestamp":"2025-07-20 22:52:37","title":"New Chat","userId":"550e8400-e29b-41d4-a716-************"}
{"level":"error","message":"Failed to initialize database service: Supabase configuration missing","service":"gita-chatbot","stack":"Error: Supabase configuration missing\n    at DatabaseService.initialize (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:16:15)\n    at initializeServices (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/app.js:42:42)\n    at startServer (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/app.js:111:9)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/app.js:206:3","timestamp":"2025-07-20 23:26:17"}
{"level":"error","message":"Failed to initialize database service: Supabase configuration missing","service":"gita-chatbot","stack":"Error: Supabase configuration missing\n    at DatabaseService.initialize (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:16:15)\n    at Server.<anonymous> (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/app.js:129:46)\n    at Object.onceWrapper (node:events:638:28)\n    at Server.emit (node:events:536:35)\n    at emitListeningNT (node:net:1955:10)\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","timestamp":"2025-07-20 23:26:17"}
