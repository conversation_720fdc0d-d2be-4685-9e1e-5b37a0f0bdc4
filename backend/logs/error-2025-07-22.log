{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching messages: invalid input syntax for type uuid: \"test123\"","service":"gita-chatbot","timestamp":"2025-07-22 02:53:53"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"<PERSON>rror creating message: invalid input syntax for type uuid: \"test123\"","service":"gita-chatbot","timestamp":"2025-07-22 02:54:00"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching messages: invalid input syntax for type uuid: \"default\"","service":"gita-chatbot","timestamp":"2025-07-22 02:54:57"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error creating message: invalid input syntax for type uuid: \"default\"","service":"gita-chatbot","timestamp":"2025-07-22 02:55:05"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error creating message: invalid input syntax for type uuid: \"default\"","service":"gita-chatbot","timestamp":"2025-07-22 02:55:42"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error creating message: invalid input syntax for type uuid: \"test123\"","service":"gita-chatbot","timestamp":"2025-07-22 03:03:51"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error creating message: invalid input syntax for type uuid: \"test123\"","service":"gita-chatbot","timestamp":"2025-07-22 03:04:44"}
{"code":"23503","details":"Key (conversation_id)=(117e2844-9a4e-4166-b603-7865f6015bdc) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:09:06"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-************) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:12:49"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-************) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:12:56"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440001) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:16:00"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440001) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:16:04"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440002) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:16:22"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440002) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:16:28"}
{"code":"23503","details":"Key (conversation_id)=(83ecf488-89fb-42c3-b431-8fa0d0c86215) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:17:05"}
{"code":"23503","details":"Key (conversation_id)=(83ecf488-89fb-42c3-b431-8fa0d0c86215) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:17:10"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440003) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:21:25"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440003) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:21:30"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440004) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:25:31"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440004) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:25:31"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440005) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:26:35"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440005) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:26:35"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440006) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:29:06"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440006) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:29:14"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440007) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:33:04"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440007) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:33:08"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440008) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:34:32"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440008) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:34:32"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440009) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:35:53"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440009) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 03:35:53"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:43:16"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:43:20"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:43:27"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:43:47"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:49:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:49:48"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:49:51"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:49:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:51:18"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:51:21"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:51:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:51:48"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:51:51"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:53:21"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:53:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:53:48"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:53:51"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:53:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:55:50"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:55:52"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:55:59"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:57:20"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:57:22"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:57:29"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:57:50"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:57:52"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:57:59"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:58:20"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:58:22"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:58:29"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:59:20"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:59:22"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:59:29"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:59:50"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 05:59:52"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:01:50"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:01:53"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:01:59"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:02:20"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:02:23"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:02:29"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:03:23"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:03:29"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:03:50"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:03:53"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:03:59"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:05:21"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:05:23"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:05:29"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:05:51"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:05:53"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:05:59"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:06:21"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:06:23"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:06:29"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:07:51"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:07:53"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:09:21"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:09:23"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:09:29"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:09:51"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:09:53"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:09:59"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:11:22"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:11:23"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:11:30"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:11:52"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:11:53"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:12:00"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:13:30"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:13:52"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:13:54"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:14:00"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:15:24"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:15:24"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:15:30"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:15:54"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:15:55"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:17:25"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:17:25"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:17:30"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:17:53"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:17:53"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:19:55"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:19:55"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:20:00"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:20:25"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:20:25"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:20:31"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:21:56"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:21:56"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:22:02"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:22:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:22:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:23:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:23:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:23:32"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:23:56"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:23:56"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:24:02"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:25:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:25:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:25:32"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:25:56"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:25:56"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:26:02"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:26:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:26:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:26:32"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:27:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:27:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:27:32"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:29:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:29:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:29:32"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:31:32"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:31:56"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:31:57"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:32:02"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:32:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:32:27"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:32:32"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:33:56"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:33:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:34:02"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:34:26"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:34:28"}
{"level":"error","message":"Error during cache cleanup: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:35:40"}
{"level":"error","message":"Error during cache cleanup: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:35:40"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:35:56"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:35:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:36:02"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:39:33"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:39:57"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:39:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:40:03"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:41:27"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:41:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:41:33"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:41:57"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:41:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:42:03"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:43:27"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:43:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:43:33"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:43:57"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:43:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:45:03"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:45:27"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:45:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:45:33"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:45:57"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:45:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:46:03"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:47:27"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:47:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:47:33"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:47:57"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:47:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:48:03"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:49:04"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:49:27"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:49:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:49:34"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:51:04"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:51:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:51:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:51:34"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:51:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:51:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:52:04"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:53:04"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:53:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:53:29"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:53:34"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:54:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:55:00"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:55:04"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:55:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:55:30"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:55:34"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:55:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:56:00"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:56:04"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:56:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:57:01"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:57:05"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:57:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:57:31"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:57:35"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:57:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:58:01"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:58:05"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:59:31"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:59:35"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 06:59:58"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 07:00:01"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 07:00:05"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 07:01:28"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 07:01:31"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 07:03:36"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 07:03:59"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 07:04:01"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 07:04:59"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 07:05:01"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 09:55:43"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-22 09:55:49"}
{"code":"23503","details":"Key (conversation_id)=(90cf3dc2-ac7b-4291-8991-e3b873bbc47a) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:21:49"}
{"code":"23503","details":"Key (conversation_id)=(90cf3dc2-ac7b-4291-8991-e3b873bbc47a) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:21:49"}
{"code":"23503","details":"Key (conversation_id)=(90cf3dc2-ac7b-4291-8991-e3b873bbc47a) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:22:47"}
{"code":"23503","details":"Key (conversation_id)=(90cf3dc2-ac7b-4291-8991-e3b873bbc47a) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:22:47"}
{"code":"23503","details":"Key (conversation_id)=(90cf3dc2-ac7b-4291-8991-e3b873bbc47a) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:37:37"}
{"code":"23503","details":"Key (conversation_id)=(90cf3dc2-ac7b-4291-8991-e3b873bbc47a) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:37:37"}
{"code":"23503","details":"Key (conversation_id)=(90cf3dc2-ac7b-4291-8991-e3b873bbc47a) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:37:38"}
{"code":"23503","details":"Key (conversation_id)=(beac11da-d3eb-48d2-b514-ffa50c791a5b) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:52:40"}
{"code":"23503","details":"Key (conversation_id)=(beac11da-d3eb-48d2-b514-ffa50c791a5b) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:52:45"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440010) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:55:27"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440010) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 11:55:31"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440011) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 12:03:52"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440011) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 12:03:57"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440012) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 12:06:12"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440012) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 12:06:12"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440013) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 12:09:17"}
{"code":"23503","details":"Key (conversation_id)=(550e8400-e29b-41d4-a716-446655440013) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 12:09:22"}
{"code":"23503","details":"Key (conversation_id)=(beac11da-d3eb-48d2-b514-ffa50c791a5b) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 12:11:32"}
{"code":"23503","details":"Key (conversation_id)=(beac11da-d3eb-48d2-b514-ffa50c791a5b) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 12:11:37"}
{"code":"23503","details":"Key (conversation_id)=(150723b8-84eb-4153-a23c-89bf19763f61) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 13:33:46"}
{"code":"23503","details":"Key (conversation_id)=(150723b8-84eb-4153-a23c-89bf19763f61) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 13:33:56"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 13:44:49"}
{"code":"23503","details":"Key (conversation_id)=(e55dd157-a4c2-4214-8cfb-67652c7c1ca2) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 14:01:52"}
{"code":"23503","details":"Key (conversation_id)=(e55dd157-a4c2-4214-8cfb-67652c7c1ca2) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 14:01:58"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:09:51"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:09:51"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:09:56"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:09:57"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:09:59"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:10:00"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:10:04"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:11:15"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:11:15"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:16:20"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:16:59"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:17:15"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:17:17"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:17:19"}
{"code":"23503","details":"Key (conversation_id)=(88d83330-cb1f-488b-9766-1c1871de9866) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 14:17:32"}
{"code":"23503","details":"Key (conversation_id)=(88d83330-cb1f-488b-9766-1c1871de9866) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 14:17:38"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:17:47"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:17:47"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Error fetching conversations: relation \"public.conversation_history\" does not exist","service":"gita-chatbot","timestamp":"2025-07-22 14:17:49"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:19:52"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:19:52"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:19:54"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:22:23"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:22:23"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:22:27"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:22:27"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:22:28"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:22:29"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:22:29"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:22:30"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:26:01"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:26:02"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:26:03"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:26:04"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:26:04"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:26:05"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:26:05"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:33:25"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:45:06"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 14:45:07"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 15:27:08"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 15:27:08"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching conversations: invalid input syntax for type uuid: \"dev-user-123\"","service":"gita-chatbot","timestamp":"2025-07-22 15:27:11"}
{"code":"23503","details":"Key (conversation_id)=(7af5aa27-eb92-453e-b1bf-51209e238577) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 15:40:25"}
{"code":"23503","details":"Key (conversation_id)=(7af5aa27-eb92-453e-b1bf-51209e238577) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 15:40:32"}
{"code":"23503","details":"Key (conversation_id)=(498ce79b-54ec-4ec1-8fa7-7e2e679c8108) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 16:03:28"}
{"code":"23503","details":"Key (conversation_id)=(498ce79b-54ec-4ec1-8fa7-7e2e679c8108) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 16:03:34"}
{"code":"23503","details":"Key (conversation_id)=(c70194e1-1e08-4919-bc27-b85a1668cffd) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 16:11:04"}
{"code":"23503","details":"Key (conversation_id)=(c70194e1-1e08-4919-bc27-b85a1668cffd) is not present in table \"conversations\".","hint":null,"level":"error","message":"Error creating message: insert or update on table \"messages\" violates foreign key constraint \"messages_conversation_id_fkey\"","service":"gita-chatbot","timestamp":"2025-07-22 16:11:08"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 1)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:643:29","timestamp":"2025-07-22 17:20:17"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 2)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:643:29","timestamp":"2025-07-22 17:20:18"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 1)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:643:29","timestamp":"2025-07-22 17:20:20"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 1)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:72:39","timestamp":"2025-07-22 17:21:42"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 1)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:643:29","timestamp":"2025-07-22 17:22:04"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 1)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:643:29","timestamp":"2025-07-22 17:22:04"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 1)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:643:29","timestamp":"2025-07-22 17:22:06"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 1)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:643:29","timestamp":"2025-07-22 17:23:22"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 1)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:643:29","timestamp":"2025-07-22 17:25:14"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 2)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:643:29","timestamp":"2025-07-22 17:25:14"}
{"level":"error","message":"Error fetching conversations: object is not iterable (cannot read property Symbol(Symbol.iterator))","service":"gita-chatbot","stack":"TypeError: object is not iterable (cannot read property Symbol(Symbol.iterator))\n    at new Set (<anonymous>)\n    at PostgrestFilterBuilder.in (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js:153:42)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:280:18\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 1)\n    at async DatabaseService.getConversations (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:270:39)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/ChatController.js:643:29","timestamp":"2025-07-22 17:25:16"}
{"level":"error","message":"Error fetching conversation with messages: Cannot read properties of null (reading 'from')","service":"gita-chatbot","stack":"TypeError: Cannot read properties of null (reading 'from')\n    at DatabaseService.getConversationWithMessages (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:332:10)\n    at testAPIEndpoint (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/test-api-endpoint.js:27:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-22 17:33:33"}
{"level":"error","message":"Error fetching conversation with messages: Cannot read properties of null (reading 'from')","service":"gita-chatbot","stack":"TypeError: Cannot read properties of null (reading 'from')\n    at DatabaseService.getConversationWithMessages (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:332:10)\n    at testAPIEndpoint (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/test-api-endpoint.js:28:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-22 17:36:25"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:21:25"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:21:26"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:21:29"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:21:33"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:21:38"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:21:39"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:21:41"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:21:46"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:22:10"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:22:11"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:22:13"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:22:18"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:22:43"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:22:44"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:22:47"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:22:51"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:23:00"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:23:01"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:23:03"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:23:08"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:23:21"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:23:22"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:23:24"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error deleting conversation: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-22 18:23:29"}
{"level":"error","message":"Error fetching conversation with messages: Cannot read properties of null (reading 'from')","service":"gita-chatbot","stack":"TypeError: Cannot read properties of null (reading 'from')\n    at DatabaseService.getConversationWithMessages (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:332:10)\n    at testConversationDetailsEndpoint (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/test-conversation-details.js:45:37)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-22 20:40:57"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:30:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-22 23:16:08"}
{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","error":"TypeError: fetch failed","level":"error","message":"❌ DB Operation: Failed to create user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-22 23:16:27"}
{"endpoint":"/stats","error":{"message":"TypeError: fetch failed"},"level":"error","message":"TypeError: fetch failed","method":"GET","service":"gita-chatbot","timestamp":"2025-07-22 23:16:27","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:30:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-22 23:16:38"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:459:9)\n    at async DatabaseService.getUserStats (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:873:57)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/UserController.js:218:21","hint":"","level":"error","message":"Error getting user stats: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-22 23:16:42"}
{"endpoint":"/stats","error":{"message":"","name":"Error","stack":"Error\n    at UserAPI.request (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/@clerk/backend/dist/index.js:1408:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:35:27)"},"level":"error","message":"","method":"GET","service":"gita-chatbot","timestamp":"2025-07-22 23:16:48","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13484:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.getUserByClerkId (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:95:31)\n    at async syncUser (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/middleware/auth.js:30:18)","hint":"","level":"error","message":"Error fetching user: TypeError: fetch failed","service":"gita-chatbot","timestamp":"2025-07-22 23:16:54"}
{"clerkId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs","error":"TypeError: fetch failed","level":"error","message":"❌ DB Operation: Failed to create user","operation":"INSERT","service":"gita-chatbot","table":"users","timestamp":"2025-07-22 23:17:23"}
{"endpoint":"/stats","error":{"message":"TypeError: fetch failed"},"level":"error","message":"TypeError: fetch failed","method":"GET","service":"gita-chatbot","timestamp":"2025-07-22 23:17:23","userId":"user_308d5EB0ciaYxDZyWBFaQYi7iHs"}
