{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-21 22:43:32"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-21 22:44:02"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-21 22:44:32"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-21 22:45:32"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-21 22:48:02"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-21 22:49:03"}
{"level":"error","message":"Cache health check failed: Command timed out","service":"gita-chatbot","stack":"Error: Command timed out\n    at Timeout._onTimeout (/home/<USER>/Desktop/HobbyProjects/gitagpt/backend/node_modules/ioredis/built/Command.js:192:33)\n    at listOnTimeout (node:internal/timers:594:17)\n    at process.processTimers (node:internal/timers:529:7)","timestamp":"2025-07-21 23:19:39"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error fetching messages: invalid input syntax for type uuid: \"test123\"","service":"gita-chatbot","timestamp":"2025-07-21 23:47:52"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error creating message: invalid input syntax for type uuid: \"test123\"","service":"gita-chatbot","timestamp":"2025-07-21 23:48:00"}
