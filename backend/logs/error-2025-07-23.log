{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error updating meditation schedule: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-23 03:48:08"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error updating meditation schedule: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-23 03:48:09"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error updating meditation schedule: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-23 03:48:11"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error updating meditation schedule: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-23 03:48:16"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error updating meditation schedule: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-23 03:48:56"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error updating meditation schedule: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-23 03:48:57"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error updating meditation schedule: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-23 03:49:00"}
{"code":"PGRST116","details":"The result contains 0 rows","hint":null,"level":"error","message":"Error updating meditation schedule: JSON object requested, multiple (or no) rows returned","service":"gita-chatbot","timestamp":"2025-07-23 03:49:04"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1069:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1023:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:26:24","timestamp":"2025-07-23 03:50:28"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1069:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1023:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:26:24","timestamp":"2025-07-23 03:50:29"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1069:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1023:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:26:24","timestamp":"2025-07-23 03:50:32"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1069:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1023:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:26:24","timestamp":"2025-07-23 03:50:36"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1069:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1023:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:26:24","timestamp":"2025-07-23 04:14:48"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1069:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1023:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:26:24","timestamp":"2025-07-23 04:14:49"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1069:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1023:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:26:24","timestamp":"2025-07-23 04:14:52"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1069:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1023:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:26:24","timestamp":"2025-07-23 04:14:56"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:20:35"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:20:36"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:20:38"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:20:43"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:23:02"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:23:03"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:23:06"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:23:10"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:24:02"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:24:04"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:24:06"}
{"level":"error","message":"Error creating meditation schedule: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.","service":"gita-chatbot","stack":"Error: Schedule conflicts with existing meditation on Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday at 07:00:00. Please choose a different time or adjust the duration.\n    at DatabaseService.checkScheduleTimeConflicts (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1068:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseService.validateScheduleCreation (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:1022:5)\n    at async DatabaseService.createMeditationSchedule (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:967:7)\n    at async file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/controllers/MeditationController.js:25:24","timestamp":"2025-07-23 04:24:11"}
{"code":"42702","details":"It could refer to either a PL/pgSQL variable or a table column.","hint":null,"level":"error","message":"Error soft deleting user: column reference \"user_id\" is ambiguous","service":"gita-chatbot","timestamp":"2025-07-23 05:06:30"}
{"code":"42702","details":"It could refer to either a PL/pgSQL variable or a table column.","hint":null,"level":"error","message":"Error soft deleting user: column reference \"user_id\" is ambiguous","service":"gita-chatbot","timestamp":"2025-07-23 05:06:31"}
{"code":"42702","details":"It could refer to either a PL/pgSQL variable or a table column.","hint":null,"level":"error","message":"Error soft deleting user: column reference \"user_id\" is ambiguous","service":"gita-chatbot","timestamp":"2025-07-23 05:06:34"}
{"code":"42702","details":"It could refer to either a PL/pgSQL variable or a table column.","hint":null,"level":"error","message":"Error soft deleting user: column reference \"user_id\" is ambiguous","service":"gita-chatbot","timestamp":"2025-07-23 05:06:38"}
{"code":"PGRST202","details":"Searched for the function public.soft_delete_user with parameters reason, target_user_id or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.","hint":"Perhaps you meant to call the function public.soft_delete_user(reason, user_id)","level":"error","message":"Error soft deleting user: Could not find the function public.soft_delete_user(reason, target_user_id) in the schema cache","service":"gita-chatbot","timestamp":"2025-07-23 05:14:50"}
{"level":"error","message":"Failed to initialize database service: Supabase configuration missing","service":"gita-chatbot","stack":"Error: Supabase configuration missing\n    at DatabaseService.initialize (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:16:15)\n    at applyMigration (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/apply-migration.js:11:20)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/apply-migration.js:48:1\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)\n    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)","timestamp":"2025-07-23 05:15:11"}
{"level":"error","message":"Failed to initialize database service: Supabase configuration missing","service":"gita-chatbot","stack":"Error: Supabase configuration missing\n    at DatabaseService.initialize (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/src/services/database.js:16:15)\n    at fixFunctions (file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/fix-functions.js:10:20)\n    at file:///home/<USER>/Desktop/HobbyProjects/gitagpt/backend/fix-functions.js:120:1\n    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:547:26)\n    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)","timestamp":"2025-07-23 05:15:40"}
{"code":"PGRST202","details":"Searched for the function public.soft_delete_user with parameters reason, target_user_id or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.","hint":"Perhaps you meant to call the function public.soft_delete_user(reason, user_id)","level":"error","message":"Error soft deleting user: Could not find the function public.soft_delete_user(reason, target_user_id) in the schema cache","service":"gita-chatbot","timestamp":"2025-07-23 05:17:34"}
{"code":"PGRST202","details":"Searched for the function public.soft_delete_user with parameters reason, target_user_id or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.","hint":"Perhaps you meant to call the function public.soft_delete_user(reason, user_id)","level":"error","message":"Error soft deleting user: Could not find the function public.soft_delete_user(reason, target_user_id) in the schema cache","service":"gita-chatbot","timestamp":"2025-07-23 05:17:35"}
{"code":"PGRST202","details":"Searched for the function public.soft_delete_user with parameters reason, target_user_id or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.","hint":"Perhaps you meant to call the function public.soft_delete_user(reason, user_id)","level":"error","message":"Error soft deleting user: Could not find the function public.soft_delete_user(reason, target_user_id) in the schema cache","service":"gita-chatbot","timestamp":"2025-07-23 05:17:37"}
{"code":"PGRST202","details":"Searched for the function public.soft_delete_user with parameters reason, target_user_id or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.","hint":"Perhaps you meant to call the function public.soft_delete_user(reason, user_id)","level":"error","message":"Error soft deleting user: Could not find the function public.soft_delete_user(reason, target_user_id) in the schema cache","service":"gita-chatbot","timestamp":"2025-07-23 05:17:41"}
{"code":"42702","details":"It could refer to either a PL/pgSQL variable or a table column.","hint":null,"level":"error","message":"Error soft deleting user: column reference \"user_id\" is ambiguous","service":"gita-chatbot","timestamp":"2025-07-23 05:23:39"}
{"code":"42702","details":"It could refer to either a PL/pgSQL variable or a table column.","hint":null,"level":"error","message":"Error soft deleting user: column reference \"user_id\" is ambiguous","service":"gita-chatbot","timestamp":"2025-07-23 05:23:40"}
{"code":"42702","details":"It could refer to either a PL/pgSQL variable or a table column.","hint":null,"level":"error","message":"Error soft deleting user: column reference \"user_id\" is ambiguous","service":"gita-chatbot","timestamp":"2025-07-23 05:23:42"}
{"code":"42702","details":"It could refer to either a PL/pgSQL variable or a table column.","hint":null,"level":"error","message":"Error soft deleting user: column reference \"user_id\" is ambiguous","service":"gita-chatbot","timestamp":"2025-07-23 05:23:46"}
